﻿using System;

namespace Apq
{
    public static class RandomNum
	{
		/// <summary>
		/// 返回[<PERSON>,<PERSON>)范围内的随机整数
		/// </summary>
		/// <param name="<PERSON>">最小值</param>
		/// <param name="<PERSON>">不包含该最大值</param>
		/// <returns></returns>
		public static int RandomInt(int Min = 0, int Max = int.MaxValue)
		{
			var r = new Random(Guid.NewGuid().GetHashCode());
			return r.Next(Min, Max);
        }

        /// <summary>
        /// 返回[<PERSON>,<PERSON>)范围内的随机浮点数
        /// </summary>
		/// <param name="<PERSON>">最小值</param>
		/// <param name="Max">不包含该最大值</param>
        /// <returns></returns>
        public static float RandomFloat(float Min = 0, float Max = float.MaxValue)
        {
            var r = new Random(Guid.NewGuid().GetHashCode());
            return Min + (Max - Min) * (float)r.NextDouble();
        }

        /// <summary>
        /// 返回[<PERSON>,<PERSON>)范围内的随机浮点数
        /// </summary>
		/// <param name="Min">最小值</param>
		/// <param name="Max">不包含该最大值</param>
        /// <returns></returns>
        public static double RandomDouble(double Min = 0, double Max = double.MaxValue)
        {
            var r = new Random(Guid.NewGuid().GetHashCode());
            return Min + (Max - Min) * r.NextDouble();
        }

        /// <summary>
        /// 返回[Min,Max)范围内的随机decimal值
        /// </summary>
		/// <param name="Min">最小值</param>
		/// <param name="Max">不包含该最大值</param>
        /// <returns></returns>
        public static decimal RandomDecimal(decimal Min = 0, decimal Max = decimal.MaxValue)
        {
            var r = new Random(Guid.NewGuid().GetHashCode());
            return Min + (Max - Min) * (decimal)r.NextDouble();
		}
    }
}
