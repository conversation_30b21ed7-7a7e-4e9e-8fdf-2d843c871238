﻿using System;
using System.Collections.Generic;

namespace Apq
{
    /// <summary>
    /// 树节点(没有上级就是根节点了)
    /// </summary>
    public class TreeNode<T>
    {
        /// <summary>
        /// 节点的数据
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// 上级节点
        /// </summary>
        public TreeNode<T> Parent { get; set; }

        /// <summary>
        /// 子级节点
        /// </summary>
        public List<TreeNode<T>> Children { get; } = new();

        /// <summary>
        /// 当前节点算第几层
        /// </summary>
        /// <remarks>一般根节点设为第0层</remarks>
        public int LayerNo { get; set; }

        /// <summary>
        /// 上一节点
        /// </summary>
        public TreeNode<T> PreNode { get; set; }
        /// <summary>
        /// 下一节点
        /// </summary>
        public TreeNode<T> NextNode { get; set; }

        /// <summary>
        /// 深度优先遍历
        /// </summary>
        /// <param name="action">(节点,第几个节点)</param>
        public TreeNode<T> Walk_Depth(Action<TreeNode<T>, int> action)
        {
            TreeWalkWatcher watcher = new();
            Walk_Depth_Imp(this, action, watcher);
            return this;
        }

        /// <summary>
        /// 深度优先遍历的递归实现
        /// </summary>
        /// <param name="action">(节点,第几个节点)</param>
        private static void Walk_Depth_Imp(TreeNode<T> node, Action<TreeNode<T>, int> action,
            TreeWalkWatcher watcher)
        {
            action.Invoke(node, watcher.Count);
            watcher.Count++;
            foreach (TreeNode<T> child in node.Children)
            {
                Walk_Depth_Imp(child, action, watcher);
            }
        }

        /// <summary>
        /// 广度优先遍历
        /// </summary>
        /// <param name="action">(节点,第几个节点)</param>
        /// <remarks>先深度遍历将结果保存到Dic中,再遍历Dic</remarks>
        public TreeNode<T> Walk_Breadth(Action<TreeNode<T>, int> action)
        {
            Dictionary<int, List<TreeNode<T>>> dic = new();

            Walk_Depth((n, i) =>
            {
                if (!dic.TryGetValue(n.LayerNo, out List<TreeNode<T>> value))
                {
                    value = new();
                    dic.Add(n.LayerNo, value);
                }

                value.Add(n);
            });

            int i = 0;
            foreach (var kv in dic)
            {
                foreach (var node in kv.Value)
                {
                    action?.Invoke(node, i++);
                }
            }

            return this;
        }

        /// <summary>
        /// 获取所有叶子节点
        /// </summary>
        /// <returns></returns>
        public IList<TreeNode<T>> GetLeafs()
        {
            List<TreeNode<T>> rtn = new();
            Walk_Depth((n, i) => rtn.Add(n));
            return rtn;
        }
    }
}
