﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

using Apq.Extension;

using CsvTables;

using UnityEditor;

using UnityEngine;

using View;

namespace Assets.Editor
{
	public class CodeGen
	{
		/// <summary>
		/// 生成角色数据枚举
		/// </summary>
		[MenuItem("工具/生成角色数据枚举")]
		public static void GenCode_ActorDataCatalog()
        {
            var actorDataCatalogCsvLoader = new ActorDataCatalogCsv();
            actorDataCatalogCsvLoader.LoadPb(true);
            
			#region 生成C#代码

			var filename = "Assets/HotScripts/DataStructure/ActorDataCatalog.cs";

			var lst = new List<string>
			{
				"namespace DataStructure",
				"{",
				"\t/// <summary>",
				"\t/// 数据类别定义",
				"\t/// </summary>",
				"\t/// <remarks>！！！这是自动生成的代码，请不要手动修改本文件！！！</remarks>",
				"\tpublic enum ActorDataCatalog",
				"\t{",
			};

			foreach (var csvRow in actorDataCatalogCsvLoader.Pb.CSVTable.Where(r =>
						 !string.IsNullOrWhiteSpace(r.EnumName)))
			{
				lst.Add(string.Empty);
				lst.Add("\t\t///<summary>");
				lst.Add($"\t\t/// {csvRow.Remark.XmlEncode()}");
				lst.Add("\t\t///</summary>");
				lst.Add($"\t\t{csvRow.EnumName} = {csvRow.Id},");
			}
			lst.Add("\t}");
			lst.Add("}");
			lst.Add(string.Empty);

			File.WriteAllText(filename, string.Join(Environment.NewLine, lst));

			#endregion

			#region 生成Lua代码

			filename = "Assets/Temp/GameLua/ActorDataCatalog.lua";

			lst = new List<string>
			{
				"-- ！！！这是自动生成的代码，请不要手动修改本文件！！！",
				string.Empty,
				"ActorDataCatalog = {",
			};

			foreach (var csvRow in actorDataCatalogCsvLoader.Pb.CSVTable.Where(r =>
						 !string.IsNullOrWhiteSpace(r.EnumName)))
			{
				lst.Add(string.Empty);
				lst.Add($"\t-- {csvRow.Remark}");
				lst.Add($"\t{csvRow.EnumName} = {csvRow.Id},");
			}
			lst.Add("};");
			lst.Add(string.Empty);

			File.WriteAllText(filename, string.Join(Environment.NewLine, lst));

			#endregion
            
            Debug.Log("生成代码成功:角色数据枚举");
		}
	}
}
