﻿namespace Apq.Web5.JsonReturn
{
	/// <summary>
	/// Json返回结构基类
	/// </summary>
	public class JsonRtnBase
	{
		/// <summary>
		/// 服务器是否执行成功(注意：有时也用于业务结果)
		/// </summary>
		public bool Success { get; set; }

		/// <summary>
		/// 状态码
		/// </summary>
		public int StatusCode { get; set; }

		/// <summary>
		/// 信息(执行成功或失败的相关描述)
		/// </summary>
		public string Message { get; set; } = string.Empty;
	}
}
