using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.IO;
using System.Collections.Generic;

public class BatchFontStyleEditor : EditorWindow
{
    [MenuItem("Tools/Batch Set Font Style to Bold")]
    public static void ShowWindow()
    {
        BatchSetFontStyleToBold();
    }

    public static void BatchSetFontStyleToBold()
    {
        string targetPath = "Assets/Temp/ui";
        List<string> modifiedPrefabs = new List<string>();
        
        Debug.Log("=== V22.0 开始批量修改Assets/Temp/ui文件夹下所有预制体Text组件字体样式为Bold ===");
        
        if (!Directory.Exists(targetPath))
        {
            Debug.LogError($"目标路径不存在: {targetPath}");
            return;
        }
        
        // 递归查找所有.prefab文件
        string[] prefabFiles = Directory.GetFiles(targetPath, "*.prefab", SearchOption.AllDirectories);
        
        Debug.Log($"V22.0 找到 {prefabFiles.Length} 个预制体文件");
        
        try
        {
            AssetDatabase.StartAssetEditing();
            
            for (int i = 0; i < prefabFiles.Length; i++)
            {
                string prefabPath = prefabFiles[i].Replace('\\', '/');
                EditorUtility.DisplayProgressBar("修改预制体字体样式", $"处理: {Path.GetFileName(prefabPath)}", (float)i / prefabFiles.Length);
                
                // 加载预制体
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    Debug.LogWarning($"V22.0 无法加载预制体: {prefabPath}");
                    continue;
                }
                
                // 实例化预制体以便修改
                GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                if (instance == null)
                {
                    Debug.LogWarning($"V22.0 无法实例化预制体: {prefabPath}");
                    continue;
                }
                
                bool hasChanges = false;
                
                try
                {
                    // 查找所有Text组件（包括子对象）
                    Text[] textComponents = instance.GetComponentsInChildren<Text>(true);
                    
                    foreach (Text textComp in textComponents)
                    {
                        if (textComp != null && textComp.fontStyle != FontStyle.Bold)
                        {
                            textComp.fontStyle = FontStyle.Bold;
                            hasChanges = true;
                            Debug.Log($"V22.0 修改Text组件字体样式为Bold: {prefabPath} -> {textComp.gameObject.name}");
                        }
                    }
                    
                    if (hasChanges)
                    {
                        // 应用修改到预制体
                        PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                        modifiedPrefabs.Add(prefabPath);
                        Debug.Log($"V22.0 成功修改预制体: {prefabPath}");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"V22.0 处理预制体时发生错误 {prefabPath}: {ex.Message}");
                }
                finally
                {
                    // 清理实例
                    DestroyImmediate(instance);
                }
            }
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
            EditorUtility.ClearProgressBar();
        }
        
        // 刷新AssetDatabase
        AssetDatabase.Refresh();
        
        Debug.Log($"=== V22.0 批量修改完成！共修改了 {modifiedPrefabs.Count} 个预制体 ===");
        
        if (modifiedPrefabs.Count > 0)
        {
            Debug.Log("V22.0 修改的预制体列表:");
            foreach (string path in modifiedPrefabs)
            {
                Debug.Log($"- {path}");
            }
        }
        
        EditorUtility.DisplayDialog("批量修改完成", 
            $"成功修改了 {modifiedPrefabs.Count} 个预制体中的Text组件字体样式为Bold。\n详细信息请查看Console窗口。", 
            "确定");
    }
} 