﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

using Apq.Extension;

namespace Apq
{
	public class Cast
	{
		#region ChangeType
#line hidden   //pdb中排除:阻止调试进入
		/// <summary>
		/// 通用类型转换,失败时返回 default(T)
		/// </summary>
		/// <typeparam name="T">输出类型</typeparam>
		/// <param name="obj">原始对象</param>
		public static T ChangeTypeT<T>(object obj) => ChangeTypeT(obj, default(T));

		/// <summary>
		/// 通用类型转换,失败时返回 fValue
		/// </summary>
		/// <typeparam name="T">输出类型</typeparam>
		/// <param name="obj">原始对象</param>
		/// <param name="fValue">转换失败时返回的值</param>
		public static T ChangeTypeT<T>(object obj, T fValue)
		{
			var (rtn, _) = TryChangeTypeT(obj, fValue);
			return rtn;
		}

		/// <summary>
		/// 通用类型转换,失败时返回失败值。
		/// </summary>
		public static object ChangeType(object obj, Type type, object fValue = default)
		{
			var (rtn, _) = TryChangeType(obj, type, fValue);
			return rtn;
		}
		#endregion

		#region TryChangeType
		/// <summary>
		/// 通用类型转换,失败时返回 default(T)
		/// </summary>
		/// <typeparam name="T">输出类型</typeparam>
		/// <param name="obj">原始对象</param>
		public static (T rtn, bool success) TryChangeTypeT<T>(object obj) =>
			TryChangeTypeT(obj, default(T));

		/// <summary>
		/// 通用类型转换,失败时返回 fValue
		/// </summary>
		/// <typeparam name="T">输出类型</typeparam>
		/// <param name="obj">原始对象</param>
		/// <param name="fValue">转换失败时返回的值</param>
		public static (T rtn, bool success) TryChangeTypeT<T>(object obj, T fValue)
		{
			// 先尝试强制类型转换(必须)
			try
			{
				return ((T)obj, true);
			}
			catch
			{
				// ignore
			}

			// 失败后使用通用类型转换
			try
			{
				var (rtn, s) = TryChangeType(obj, typeof(T), fValue);
				return ((T)rtn, s);
			}
			catch
			{
				// 失败则返回fValue
				return (fValue, false);
			}
		}

		/// <summary>
		/// 通用类型转换,失败时返回失败值。
		/// </summary>
		public static (object rtn, bool success) TryChangeType(object obj, Type type, object fValue = default)
		{
			try
			{
				// 来源是null时转为null或值类型的默认值
				if (obj == null)
				{
					return type.IsValueType ? (Activator.CreateInstance(type), true)
						: (null, true);
				}

				var objType = obj.GetType();

				// 来源是可空类型,取出其原始类型和值
				if (objType.IsNullable())
				{
					Type oType;
					// ReSharper disable once RedundantSuppressNullableWarningExpression
					(obj, oType) = obj!.GetOriginValueInNullable();

					// 来源是null时转为null或值类型的默认值
					if (obj == null)
					{
						return type.IsValueType ? (Activator.CreateInstance(type), true)
							: (null, true);
					}

					objType = oType!;
				}

				var nullableType = type;//如果转换结果需要转换成可空类型,这里保存可空类型的Type
				var needNullable = type.IsNullable();
				var success = false;//是否转换成功
				object rtn = null;//转换后的值

				// 目标是可空类型
				if (needNullable)
				{
					// 取出其原始类型
					type = Nullable.GetUnderlyingType(type)!;
				}

				// 来源可直接分配给目标时,不用转换
				if (type.IsAssignableFrom(objType))
				{
					rtn = obj;
					success = true;
				}
				// 转为字符串时调用来源的ToString方法
				else if (typeof(string).IsAssignableFrom(type))
				{
					rtn = obj.ToString();
					success = true;
				}
				// 转为枚举类型时,如果源值是字符串,自动解析
				else if (type.IsEnum)
				{
					try
					{
						rtn = obj is string s ? Enum.Parse(type, s) : Enum.ToObject(type, obj);
						success = true;
					}
					catch
					{
						success = false;
					}
				}

				if (!success && typeof(string).IsAssignableFrom(objType))
				{
					// 来源是字符串时,尝试调用目标类型的静态Parse(string)方法
					try
					{
						var parseMethod = type.GetMethods(BindingFlags.Public | BindingFlags.Static)
							.FirstOrDefault(x =>
								x.Name == "Parse"
								&& x.GetParameters().Length == 1
								&& typeof(string).IsAssignableFrom(x.GetParameters()[0].ParameterType));
						if (parseMethod != null)
						{
							rtn = parseMethod.Invoke(null, new[] { obj });
							success = true;
						}
					}
					catch
					{
						success = false;
					}
				}

				if (!success)
				{
					// 再尝试一下Convert.ChangeType
					try
					{
						rtn = Convert.ChangeType(obj, type);
						success = true;
					}
					catch
					{
						success = false;
					}
				}

				if (!success)
				{
					return (fValue, false);
				}

				if (needNullable)
				{
					rtn = Activator.CreateInstance(nullableType, new[] { rtn });
				}

				return (rtn, true);
			}
			catch
			{
				return (fValue, false);
			}
		}
#line default
		#endregion

		#region ToExcelObject
		/// <summary>
		/// 任意值转换到 Excel 能接受的值
		/// </summary>
		public static object ToExcelObject(object obj)
		{
			switch (obj)
			{
				case DBNull _:
					return string.Empty;
				case bool _:
				case byte _:
					{
						var n = Convert.ToInt32(obj);
						return ToExcelObject(n);
					}
				case Guid _:
					return obj.ToString();
				case byte[] aryBytes:
					return "0x" + BytesToHexString(aryBytes);
				default:
					return obj;
			}

		}
		#endregion

		#region BytesToHexString
		/// <summary>
		/// 将字节串转换为16进制字符串
		/// </summary>
		public static string BytesToHexString(ICollection<byte> input)
		{
			var sb = new StringBuilder();
			foreach (var b in input)
			{
				sb.Append(b.ToString("X2"));
			}
			return sb.ToString();
		}
		#endregion

		#region HexStringToBytes
		/// <summary>
		/// 将16进制字符串转换为字节串
		/// </summary>
		public static byte[] HexStringToBytes(string input)
		{
			// 去掉0x前缀
			while (input.StartsWith("0x"))
			{
				input = input[2..];
			}

			if (input.Length <= 1) return null;
			var lst = new byte[input.Length / 2];
			for (int i = 0, j = 2; j <= input.Length; i += 2, j += 2)
			{
				var str = input.Substring(i, 2);
				lst[i / 2] = Convert.ToByte(str, 16);
			}
			return lst;
		}
		/// <summary>
		/// 将16进制字符串转换为字节串
		/// </summary>
		public static IList<byte> HexStringToBytesList(string input)
		{
			// 去掉0x前缀
			while (input.StartsWith("0x"))
			{
				input = input[2..];
			}

			var lst = new List<byte>(input.Length / 2);
			for (var i = 2; i <= input.Length; i += 2)
			{
				var str = input.Substring(i - 2, 2);
				lst.Add(Convert.ToByte(str, 16));
			}
			return lst;
		}
		#endregion
	}
}
