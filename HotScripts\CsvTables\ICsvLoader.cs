﻿// ReSharper disable StaticMemberInGenericType

using System.Collections.Generic;

/// <summary>
/// CSV表格加载器接口(基类)
/// </summary>
public interface ICsvLoader
{
	/// <summary>
	/// 加载Pb到内存
	/// </summary>
	/// <param name="forceReload">是否强制重新加载</param>
	public void LoadPb(bool forceReload = false);
}

/// <summary>
/// CSV表格加载器接口
/// </summary>
/// <typeparam name="T">Message类型(表)</typeparam>
/// <typeparam name="TRow">单行类型</typeparam>
/// <typeparam name="TKey">主键列类型</typeparam>
public interface ICsvLoader<out T, TRow, TKey> : ICsvLoader
{
	/// <summary>
	/// 获取已加载的pb内容
	/// </summary>
	T Pb { get; }

	/// <summary>
	/// 获取从Pb转换的字典
	/// </summary>
	public Dictionary<TKey, TRow> Dic
	{
		get;
	}
}
