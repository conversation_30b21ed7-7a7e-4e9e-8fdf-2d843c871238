﻿using System;
using System.Threading;

using Cysharp.Threading.Tasks;

using Thing;

namespace BuffActuators
{
	/// <summary>
	/// Buff执行器基类(循环类功能的Buff才需要,一轮功能完成之后就销毁)
	/// </summary>
	public class BuffActuatorBase : IDisposable
	{
		/// <summary>
		/// 哪个Buff的执行者
		/// </summary>
		public BuffThing Buff { get; set; }

		/// <summary>
		/// 本轮取消令牌
		/// </summary>
		public CancellationTokenSource CTS_Actuator { get; } = new();

		#region IDisposable
		protected bool disposedValue;
		/// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
		protected virtual void Dispose(bool disposing)
		{
			if (!disposedValue)
			{
				if (disposing)
				{
					DoDispose();
				}

				// TODO: 释放未托管的资源(未托管的对象)并重写终结器
				// TODO: 将大型字段设置为 null
				disposedValue = true;
			}
		}

		// // TODO: 仅当“Dispose(bool disposing)”拥有用于释放未托管资源的代码时才替代终结器
		// ~CreatureSkillBase()
		// {
		//     // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
		//     Dispose(false);
		// }

		public void Dispose()
		{
			// 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
			Dispose(true);
			GC.SuppressFinalize(this);
		}
		#endregion

		/// <summary>
		/// 启动Buff的功能
		/// </summary>
		public virtual async UniTaskVoid StartBuff(CancellationToken token)
		{
			await UniTask.SwitchToMainThread();
		}

		/// <summary>
		/// 立即停止本轮执行
		/// </summary>
		public virtual void StopBuff()
		{
			Dispose();
		}

		protected virtual void DoDispose()
		{
			if (!CTS_Actuator.IsCancellationRequested)
			{
				CTS_Actuator.Cancel();
			}
			ClearActuator();
		}

		/// <summary>
		/// 清除本轮执行器对场景的所有影响(比如,销毁或还回对象池等)
		/// </summary>
		public virtual void ClearActuator()
		{
		}
	}
}
