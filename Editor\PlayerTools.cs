using DG.Tweening.Plugins.Core.PathCore;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Security.Policy;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;

public class PlayerTools : Editor
{


    [MenuItem("工具/删除数据/删除引导第1步")]
    public static void DeleteStep1()
    {
        PlayerPrefs.DeleteKey("newGuide4");
        PlayerPrefs.DeleteKey("Result4");
        PlayerPrefs.DeleteKey("GuideCount4");
        Debug.Log("删除引导第1步数据");
    }

    [MenuItem("工具/删除数据/删除引导第2步")]
    public static void DeleteStep2()
    {
        PlayerPrefs.DeleteKey("newGuide5");
        PlayerPrefs.DeleteKey("Result5");
        PlayerPrefs.DeleteKey("GuideCount5");
        Debug.Log("删除引导第2步数据");
    }

    [MenuItem("工具/删除数据/删除引导第3步")]
    public static void DeleteStep3()
    {
        PlayerPrefs.DeleteKey("newGuide6");
        PlayerPrefs.DeleteKey("Result6");
        PlayerPrefs.DeleteKey("GuideCount6");
        Debug.Log("删除引导第3步数据");
    }
    [MenuItem("工具/删除数据/删除引导第4步")]
    public static void DeleteStep4()
    {
        PlayerPrefs.DeleteKey("newGuide7");
        PlayerPrefs.DeleteKey("Result7");
        PlayerPrefs.DeleteKey("GuideCount7");
        Debug.Log("删除引导第4步数据");
    }
    [MenuItem("工具/删除数据/删除引导第5步")]
    public static void DeleteStep5()
    {
        PlayerPrefs.DeleteKey("newGuide8");
        PlayerPrefs.DeleteKey("Result8");
        PlayerPrefs.DeleteKey("GuideCount8");
        Debug.Log("删除引导第5步数据");
    }
    [MenuItem("工具/删除数据/删除引导第6步")]
    public static void DeleteStep6()
    {
        PlayerPrefs.DeleteKey("newGuide9");
        PlayerPrefs.DeleteKey("Result9");
        PlayerPrefs.DeleteKey("GuideCount9");
        Debug.Log("删除引导第6步数据");
    }
    [MenuItem("工具/删除数据/删除引导第7步")]
    public static void DeleteStep7()
    {
        PlayerPrefs.DeleteKey("newGuide10");
        PlayerPrefs.DeleteKey("Result10");
        PlayerPrefs.DeleteKey("GuideCount10");
        Debug.Log("删除引导第7步数据");
    }
    [MenuItem("工具/删除数据/删除引导第8步")]
    public static void DeleteStep8()
    {
        PlayerPrefs.DeleteKey("newGuide11");
        PlayerPrefs.DeleteKey("Result11");
        PlayerPrefs.DeleteKey("GuideCount11");
        Debug.Log("删除引导第8步数据");
    }

    [MenuItem("工具/删除数据/删除批量合成引导")]
    public static void DeleteBatchCompound()
    {
        PlayerPrefs.DeleteKey("newGuide12");
        PlayerPrefs.DeleteKey("Result12");
        PlayerPrefs.DeleteKey("GuideCount12");
        Debug.Log("删除批量合成引导");
    }
    [MenuItem("工具/删除数据/删除批量分解引导")]
    public static void DeleteBatchResolve()
    {
        PlayerPrefs.DeleteKey("newGuide13");
        PlayerPrefs.DeleteKey("Result13");
        PlayerPrefs.DeleteKey("GuideCount13");
        Debug.Log("删除批量合成引导");
    }

    [MenuItem("工具/删除数据/删除全部本地引导数据")]
    public static void DeleteAll()
    {
        for(int i = 4; i < 14; i++)
        {
            string saveName = "newGuide" + i;
            PlayerPrefs.DeleteKey(saveName);
            saveName = "Result" + i;
            PlayerPrefs.DeleteKey(saveName);
            saveName = "GuideCount" + i;
            PlayerPrefs.DeleteKey(saveName);
        }
        Debug.Log("删除所有本地数据");
    }

    //[MenuItem("工具/lua打包")]
    //public static void LuaPack()
    //{
    //    string luaDir = Application.dataPath+"/Temp/GameLua";
    //    string outDir = Application.dataPath.Replace("Assets", "ServerData/lua.bytes");
    //    Debug.Log(luaDir);
    //    Debug.Log(outDir);

    //    AssetBundle bundle = AssetBundle.LoadFromFile("Assets/StreamingAssets/lua.unity3d");
    //    TextAsset textAsset = bundle.LoadAsset<TextAsset>("Main.lua");
    //    Debug.Log(textAsset.text);
    //}
    

    [MenuItem("工具/准备lua打包资源")]
    public static void LuaPatch()
    {
        Encoding utf8WithoutBom = new UTF8Encoding(false);

        string luaDir = Application.dataPath + "/Temp/GameLua";
        string outDir = Application.dataPath + "/Temp/GameLua_";
        string toluaDir = Application.dataPath + "/Temp/res/Lua";
        string pbDir = Application.dataPath + "/Temp/pb";

        Directory.CreateDirectory(outDir);
        string[] files = Directory.GetFiles(luaDir,"*.lua");
        foreach (string file in files) 
        {
            //byte[] data = File.ReadAllBytes(file);
            string outputFile = outDir + "/" + file.Replace(luaDir + "\\","") + ".bytes";
            //Debug.Log(outputFile);
            File.WriteAllText(outputFile, File.ReadAllText(file), utf8WithoutBom);
        }
        AssetDatabase.Refresh();

        files = Directory.GetFiles(outDir, "*.bytes");
        foreach (string file in files)
        {
            //Debug.Log(file.Replace(Application.dataPath, "Assets"));
            AssetImporter.GetAtPath(file.Replace(Application.dataPath, "Assets")).assetBundleName = "lua.bytes";
        }

        files = Directory.GetFiles(toluaDir, "*.bytes",SearchOption.AllDirectories);
        foreach (string file in files)
        {
            //Debug.Log(file.Replace(Application.dataPath, "Assets"));
            AssetImporter.GetAtPath(file.Replace(Application.dataPath, "Assets")).assetBundleName = "lua.bytes";
        }

        files = Directory.GetFiles(pbDir, "*.bytes", SearchOption.AllDirectories);
        foreach (string file in files)
        {
            //Debug.Log(file.Replace(Application.dataPath, "Assets"));
            AssetImporter.GetAtPath(file.Replace(Application.dataPath, "Assets")).assetBundleName = "pb.bytes";
        }

        AssetDatabase.Refresh();
    }

    [MenuItem("工具/GameLua目录下lua脚本编码格式改成无bom头的utf-8")]
    public static void LuaToUTF()
    {
        Encoding utf8WithoutBom = new UTF8Encoding(false);
        string luaDir = Application.dataPath + "/Temp/GameLua";

        //File.WriteAllText(luaDir, File.ReadAllText(luaDir), Encoding.UTF8);
        string[] toLuaFiles = Directory.GetFiles(luaDir, "*.lua", SearchOption.AllDirectories);
        foreach (string file in toLuaFiles)
        {
            //Debug.Log(file);
            File.WriteAllText(file, File.ReadAllText(file), utf8WithoutBom);
        }
        AssetDatabase.Refresh();
        Debug.Log("lua文件去掉bom头完成！！！");
    }

    [MenuItem("工具/给ui文本对象添加TextAssist字体控件")]
    public static void AddTextAssitToUI()
    {
        BrowseAllFiles("Assets/Temp/ui");
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    private static void BrowseAllFiles(string path)
    {
        string[] dirs = Directory.GetDirectories(path);
        foreach (var d in dirs)
        {
            if (d != path)
                BrowseAllFiles(d);
        }

        string[] files = Directory.GetFiles(path);
        foreach (var e in files)
        {
            if (e.EndsWith(".prefab"))
            {
                CheckPrefabFile(e);
            }
        }
    }
    private static bool CheckPrefabFile(string path)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
        if (!prefab)
        {
            Debug.LogWarning("CheckPrefabFile 找不到路径 " + path);
            return false;
        }

        StringBuilder sbErr = new StringBuilder();
        CheckChildPrefabFile(prefab.transform, sbErr);
        if (sbErr.Length > 0)
        {
            Debug.LogError("添加控件! " + path);
            Debug.Log(sbErr.ToString());
        }

        return true;
    }
    // 递归处理子节点
    public static void CheckChildPrefabFile(Transform go, StringBuilder sb)
    {
        if (!go)
            return;
        UnityEngine.UI.Text txt = go.GetComponent<UnityEngine.UI.Text>();
        if (txt)
        {
            if (txt.GetComponent<TextAssist>() == null)
            {
                txt.gameObject.AddComponent<TextAssist>();
                sb.Append(txt.name + "\n");
            }
        }

        TMPro.TextMeshProUGUI tmp = go.GetComponent<TMPro.TextMeshProUGUI>();
        if (tmp)
        {
            if (tmp.GetComponent<TextAssist>() == null)
            {
                tmp.gameObject.AddComponent<TextAssist>();
                sb.Append(tmp.name + "\n");
            }
        }

        for (int i = 0; i < go.childCount; ++i)
        {
            CheckChildPrefabFile(go.GetChild(i), sb);
        }
    }
}
