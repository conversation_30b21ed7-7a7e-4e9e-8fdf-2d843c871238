﻿using Cysharp.Threading.Tasks;
using System.Threading;
using Thing;

namespace BuffActuators
{
	/// <summary>
	/// 流血功能
	/// </summary>
	public class BuffActuator_1001 : BuffActuatorBase
	{
		/// <summary>
		/// 启动Buff的功能
		/// </summary>
		public override async UniTaskVoid StartBuff(CancellationToken token)
		{
			await UniTask.SwitchToMainThread();

			var cts = CancellationTokenSource.CreateLinkedTokenSource(token, CTS_Actuator.Token);

			DoHpLoss(cts.Token).Forget();
		}

		/// <summary>
		/// 承受者流血
		/// </summary>
		public virtual async UniTaskVoid DoHpLoss(CancellationToken token)
		{
			await UniTask.SwitchToMainThread();

			if (Buff.Bearer is CreatureThing creature)
			{
				var damage = Helper.CalcDamage(Buff);
				creature.TakeHit(Buff.Gun, damage);
			}
		}
	}
}
