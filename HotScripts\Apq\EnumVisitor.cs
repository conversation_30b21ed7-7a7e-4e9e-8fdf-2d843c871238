﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;

namespace Apq
{
	/// <summary>
	/// 枚举遍历器
	/// </summary>
	/// <typeparam name="T">枚举类型</typeparam>
	public class EnumVisitor<T> : IEnumerable<T>
	{
		/// <summary>
		/// 返回类型为 IEnumerable&lt;T&gt; 的输入。
		/// </summary>
		/// <returns>类型为 IEnumerable&lt;T&gt; 的序列。</returns>
		public static IEnumerable<T> AsEnumerable()
		{
			return new EnumVisitor<T>();
		}

		#region IEnumerable<T>
		/// <summary>
		/// 返回一个循环访问集合的枚举数。
		/// </summary>
		/// <returns>可用于循环访问集合的 IEnumerator 。</returns>
		public IEnumerator<T> GetEnumerator()
		{
			return Enum.GetValues(typeof(T)).Cast<T>().GetEnumerator();
		}

		/// <summary>
		/// 返回一个循环访问集合的枚举数。
		/// </summary>
		/// <returns>可用于循环访问集合的 IEnumerator 。</returns>
		IEnumerator IEnumerable.GetEnumerator()
		{
			return GetEnumerator();
		}
		#endregion

		/// <summary>
		/// 以三列DataTable(Name,Value{long},Description)输出枚举的所有值
		/// </summary>
		/// <returns></returns>
		public DataTable ToDataTable()
		{
			var dt = new DataTable();
			dt.Columns.Add("Name");
			dt.Columns.Add("Value", typeof(long));
			dt.Columns.Add("Description");

			var t = typeof(T);
			foreach (var eValue in Enum.GetValues(t))
			{
				var name = eValue.ToString();
				var value = (int)eValue;
				var description = name;

				var field = t.GetField(name!);
				if (Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute)) is DescriptionAttribute attr)
				{
					description = attr.Description;
				}

				var dr = dt.NewRow();
				dr["Name"] = name;
				dr["Value"] = value;
				dr["Description"] = description;
				dt.Rows.Add(dr);
			}

			return dt;
		}

		/// <summary>
		/// 以EnumRow(Name,Value{long},Description)的列表输出枚举的所有值
		/// </summary>
		/// <returns></returns>
		public List<EnumRow> ToList()
		{
			var lst = new List<EnumRow>();

			var t = typeof(T);
			foreach (var eValue in Enum.GetValues(t))
			{
				var dr = new EnumRow() { Name = eValue.ToString()!, Value = (int)eValue };

				dr.Description = dr.Name;
				var field = t.GetField(dr.Name);
				if (Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute)) is DescriptionAttribute attr)
				{
					dr.Description = attr.Description;
				}

				lst.Add(dr);
			}

			return lst;
		}
	}
}
