using System;
using System.Collections.Generic;
using System.IO;

using UnityEditor;
using UnityEditor.Build.Reporting;
using UnityEditor.Callbacks;

using UnityEngine;
#if UNITY_IOS
    using UnityEditor.iOS.Xcode;
#endif


public class BuildClientWizard : EditorWindow
{
    public static string GetArg(string name)
    {
        var args = System.Environment.GetCommandLineArgs();
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == name && args.Length > i + 1)
            {
                return args[i + 1];
            }
        }
        return null;
    }
    public static void BulidAPK(string var)
    {
        // read the "-outputDir" command line argument
        var outputDir = GetArg("-output") ?? Application.dataPath.Replace("Assets", "apk/");
        var name = GetArg("-name") ?? "Explottens";
        var publisher = GetArg("-publisher");
        var identifier = GetArg("-identifier") ?? "com.deer.xyz";
        var ver = GetArg("-version") ?? var;
        var code = GetArg("-versionCode") ?? "0";
        string symbol = GetArg("-define");
        string defines = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
        if (symbol != "")
        {
            PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, (defines + ";" + symbol));
        }
        var target = GetArg("-target") ?? "Android";
        BulidTarget(name, publisher, ver, target, outputDir, identifier, BuildOptions.None, code);
    }
    public static void ExportProject()
    {
        // read the "-outputDir" command line argument
        var outputDir = GetArg("-output");
        var name = GetArg("-name");
        var publisher = GetArg("-publisher");
        var identifier = GetArg("-identifier");
        var ver = GetArg("-version");
        var code = GetArg("-versionCode");
        var target = GetArg("-target");
        BulidTarget(name, publisher, ver, target, outputDir, identifier, BuildOptions.AcceptExternalModificationsToPlayer, code);
    }
    //这里封装了一个简单的通用方法。
    public static void BulidTarget(string name, string publicer, string ver, string target, string outputdir, string identifier, BuildOptions option, string code)
    {
        string app_name = name + ver;
        string target_dir = outputdir;
        string target_name = null;
        BuildTargetGroup targetGroup = BuildTargetGroup.Android;
        BuildTarget buildTarget = BuildTarget.Android;
        //string applicationPath = Application.dataPath.Replace("/Assets", "");
        string logofolderPre = "Assets/Temp/logo/";
        string suffix = ".png";
        if (target.Equals("android", StringComparison.CurrentCultureIgnoreCase))
        {
            target_name = app_name + ".apk";
            targetGroup = BuildTargetGroup.Android;
            PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64;
            // Texture2D[] txt = {
            //      AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "192" + suffix)
            //     ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "144" + suffix)
            //     ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "96" + suffix)
            //     ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "72" + suffix)
            //     ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "48" + suffix)
            //     ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "36" + suffix)};
            // PlayerSettings.SetIconsForTargetGroup(targetGroup, txt);
        }
        else if (target.Equals("ios", StringComparison.CurrentCultureIgnoreCase))
        {
            target_name = app_name;
            targetGroup = BuildTargetGroup.iOS;
            buildTarget = BuildTarget.iOS;
            PlayerSettings.iOS.targetDevice = iOSTargetDevice.iPhoneAndiPad;
            PlayerSettings.iOS.allowHTTPDownload = true;
            PlayerSettings.iOS.appleEnableAutomaticSigning = true;
            PlayerSettings.iOS.appleDeveloperTeamID = "cp zhang";
            PlayerSettings.aotOptions = "nrgctx-trampolines=9012,nimt-trampolines=9012,ntrampolines=9012";
            PlayerSettings.iOS.scriptCallOptimization = ScriptCallOptimizationLevel.SlowAndSafe;
            PlayerSettings.iOS.targetOSVersionString = "8.0";
            PlayerSettings.stripEngineCode = false;
            PlayerSettings.iOS.applicationDisplayName = name;
            Texture2D[] txt = {
                 AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "180" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "167" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "152" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "144" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "120" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "114" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "76" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "72" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "57" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "80" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "40" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "87" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "58" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "29" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "60" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "20" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "1024" + suffix)}
            ;
            PlayerSettings.SetIconsForTargetGroup(targetGroup, txt);
        }
        else if (target.Equals("windows", StringComparison.CurrentCultureIgnoreCase))
        {
            target_name = app_name;
            targetGroup = BuildTargetGroup.Standalone;
            buildTarget = BuildTarget.StandaloneWindows;
            Texture2D[] txt = {
                 AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "192" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "144" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "96" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "72" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "48" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "36" + suffix)};
            PlayerSettings.SetIconsForTargetGroup(targetGroup, txt);
        }
        else if (target.Equals("windows64", StringComparison.CurrentCultureIgnoreCase))
        {
            target_name = app_name;
            targetGroup = BuildTargetGroup.Standalone;
            buildTarget = BuildTarget.StandaloneWindows64;
            Texture2D[] txt = {
                 AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "192" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "144" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "96" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "72" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "48" + suffix)
                ,AssetDatabase.LoadAssetAtPath<Texture2D>(logofolderPre + "36" + suffix)};
            PlayerSettings.SetIconsForTargetGroup(targetGroup, txt);
        }
        else
        {
            Debug.LogError("only android or ios supported!");
            return;
        }

        if (AssetBundleGenerator.isExportAndroidProject)
        {
            target_name = "AndroidProject";
        }
        //每次build删除之前的残留
        if (Directory.Exists(target_dir))
        {
            if (File.Exists(target_name))
            {
                File.Delete(target_name);
            }
        }
        else
        {
            Directory.CreateDirectory(target_dir);
        }
        PlayerSettings.bundleVersion = ver;
        EditorUserBuildSettings.exportAsGoogleAndroidProject = AssetBundleGenerator.isExportAndroidProject;
        PlayerSettings.SplashScreen.show = false;
        //PlayerSettings.Android.keystoreName = "..\\bpm.keystore";
        //PlayerSettings.Android.keyaliasName = "client";
        //PlayerSettings.keystorePass = "123456";
        //PlayerSettings.keyaliasPass = "123456";
        PlayerSettings.applicationIdentifier = "com.deer.xyz";
        // Player Setting
        /*PlayerSettings.companyName = publicer;
        PlayerSettings.productName = name;
        PlayerSettings.GetIconSizesForTargetGroup(targetGroup);
        PlayerSettings.Android.keystoreName = "F:/DKClientBuild/打包签名/debug.keystore";
        PlayerSettings.Android.keyaliasName = "androiddebugkey";
        PlayerSettings.keystorePass = "android";
        PlayerSettings.keyaliasPass = "android";
        PlayerSettings.defaultInterfaceOrientation = UIOrientation.Portrait;
        PlayerSettings.allowedAutorotateToLandscapeLeft = false;
        PlayerSettings.allowedAutorotateToLandscapeRight = false;
        PlayerSettings.allowedAutorotateToPortrait = true;
        PlayerSettings.allowedAutorotateToPortraitUpsideDown = true;
        PlayerSettings.statusBarHidden = true;
		//PlayerSettings.SplashScreen.show = false;
        PlayerSettings.use32BitDisplayBuffer = false;
        
        PlayerSettings.bundleVersion = ver;
        PlayerSettings.Android.bundleVersionCode = int.Parse(code);
        PlayerSettings.forceSingleInstance = true;
        PlayerSettings.stripUnusedMeshComponents = true;
        
        PlayerSettings.MTRendering = true;
        PlayerSettings.applicationIdentifier = identifier;*/
        //得到工程中所有场景名称
        string[] SCENES = FindEnabledEditorScenes();
        //开始Build场景，等待吧～
        GenericBuild(SCENES, target_dir + "/" + target_name, targetGroup, buildTarget, option);
        // 反定义宏
        PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, "");
    }

    private static string[] FindEnabledEditorScenes()
    {
        List<string> EditorScenes = new List<string>();
        foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes)
        {
            if (!scene.enabled) continue;
            EditorScenes.Add(scene.path);
        }
        return EditorScenes.ToArray();
    }

    static void GenericBuild(string[] scenes, string target_dir, BuildTargetGroup targetGroup, BuildTarget build_target, BuildOptions build_options)
    {
        Debug.Log("target_dir__" + target_dir + "_build_target_" + build_target);
        //EditorUserBuildSettings.SwitchActiveBuildTarget(targetGroup, build_target);
        var res = BuildPipeline.BuildPlayer(scenes, target_dir, build_target, build_options);

        if (res.summary.result == BuildResult.Failed)
        {
            throw new Exception("BuildPlayer failure: " + res);
        }
    }
    [PostProcessBuild]
    public static void OnPostprocessBuild(BuildTarget buildTarget, string path)
    {
#if UNITY_IOS
        if (buildTarget == BuildTarget.iOS)
        {
            string projPath = PBXProject.GetPBXProjectPath(path);
            PBXProject proj = new PBXProject();
            proj.ReadFromString(File.ReadAllText(projPath));

            string target = proj.TargetGuidByName("Unity-iPhone");

            proj.SetBuildProperty(target, "ENABLE_BITCODE", "NO");
            proj.SetBuildProperty(target, "GCC_ENABLE_OBJC_EXCEPTIONS", "YES");
            proj.SetBuildProperty(target, "LD_RUNPATH_SEARCH_PATHS", "@executable_path/Frameworks @executable_path/ ${inherited}");
            proj.AddBuildProperty(target, "OTHER_LDFLAGS", "-ObjC");

            proj.AddFrameworkToProject(target, "libz.tbd", true);
            proj.AddFrameworkToProject(target, "libc.tbd", true);
            proj.AddFrameworkToProject(target, "CoreMotion.framework", true);
            proj.AddFrameworkToProject(target, "Foundation.framework", true);
            proj.AddFrameworkToProject(target, "UIKit.framework", true);
            proj.AddFrameworkToProject(target, "SystemConfiguration.framework", true);

            File.WriteAllText(projPath, proj.WriteToString());
        }
#endif

    }
}
