﻿// ReSharper disable NonReadonlyMemberInGetHashCode

using System;

namespace Apq
{
    /// <summary>
    /// 表示C#编程中某个对象具有某个名称的成员
    /// </summary>
    public class CSMember : IEquatable<CSMember>  //可以进行相等比较
                                //,IComparer<CSMember>//大小比较器
                                //,IComparable<CSMember>//可以进行大小比较
                                //,
    {
	    /// <summary>
	    /// 表示C#编程中某个对象具有某个名称的成员
	    /// </summary>
	    public CSMember(object Obj = null, string MemberName = "")
	    {
		    this.Obj = Obj;
		    this.MemberName = MemberName;
	    }

	    /// <summary>
        /// 获取或设置对象
        /// </summary>
        public object Obj { get; }

        /// <summary>
        /// 获取或设置成员名
        /// </summary>
        public string MemberName { get; set; }

        #region 相等比较
        #region 接口
        /// <summary>
        /// 相等比较
        /// </summary>
        public bool Equals(CSMember other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Equals(other.Obj, Obj) && Equals(other.MemberName, MemberName);
        }

        /// <summary>
        /// 获取哈希值
        /// </summary>
        /// <returns></returns>
        public override int GetHashCode()
        {
            var nObj = Obj == null ? 0 : Obj.GetHashCode();
            var nMemberName = MemberName.GetHashCode();
            return nObj ^ nMemberName;
        }
        #endregion

        #region ==/!=
        /// <summary>
        /// 相等比较
        /// </summary>
        public override bool Equals(object obj)
        {
            if (obj is null) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj.GetType() == typeof(CSMember) && Equals((CSMember)obj);
        }

        /// <summary>
        /// ==运算符
        /// </summary>
        public static bool operator ==(CSMember left, CSMember right)
        {
            return Equals(left, right);
        }
        /// <summary>
        /// !=运算符
        /// </summary>
        public static bool operator !=(CSMember left, CSMember right)
        {
            return !Equals(left, right);
        }
        #endregion
        #endregion
    }
}
