# Spine预制体批量Transform修改工具 V2.0

## 🎯 V2.0版本重大改进

**用户反馈优化：**
1. ✅ **快速批量选择** - 支持搜索过滤、模式匹配、一键选择同类预制体
2. ✅ **统一节点修改** - 预制体内所有Spine节点统一处理，无需单独设置
3. ✅ **一键全局修改** - 设置一次参数，所有选中预制体一起批量修改

## 工具概述

该工具用于批量修改`Assets/Temp/model/spine`和`Assets/Temp/model/uiSpine`文件夹中预制体内`Spine_XiaoGua_xxx`节点的Transform参数（Position、Rotation、Scale）。

## 核心功能特性

### 🚀 快速选择系统
- **智能搜索过滤**：输入关键词自动筛选和选中匹配的预制体
- **模式匹配选择**：一键选择所有JueSe_*、Spine_XiaoGua_*、UIJueSe_*等同类预制体
- **自定义模式**：支持自定义前缀模式批量选择
- **反选功能**：快速反转当前选择状态

### ⚡ 统一修改系统
- **预制体级别操作**：每个预制体内所有Spine_XiaoGua_xxx节点统一修改
- **双重修改模式**：
  - **偏移模式**：在原值基础上增加偏移量
  - **绝对值模式**：直接覆盖为指定的绝对值
- **一键批量应用**：设置一次参数，所有选中预制体同时修改

### 🛡️ 安全操作保障
- **确认对话框**：修改前显示详细信息和确认对话框
- **实时统计显示**：显示选中预制体数量和Spine节点总数
- **完整的撤销机制**：修改前可随时重置所有设置

## 使用步骤

### 1. 打开工具
在Unity编辑器顶部菜单栏中，选择`Tools` → `Spine预制体批量Transform修改工具`

### 2. 快速选择预制体

#### 方法一：搜索过滤选择
1. 在"搜索过滤"框中输入关键词（如：`JueSe_001`）
2. 工具会自动选中所有包含该关键词的预制体
3. 点击"清除"按钮清空搜索并取消所有选择

#### 方法二：模式匹配选择
1. 点击预设的模式按钮：
   - **选择所有JueSe_***：选中所有角色预制体
   - **选择所有Spine_XiaoGua_***：选中所有Spine预制体
   - **选择所有UIJueSe_***：选中所有UI角色预制体
2. 或者在"自定义模式"框中输入前缀，点击"选择匹配项"

#### 方法三：手动单选
1. 在预制体列表中逐个勾选需要的预制体
2. 选中的预制体会高亮显示，便于识别

### 3. 设置Transform修改参数

#### 偏移模式（推荐）
1. 确保"使用绝对值（覆盖原值）"未勾选
2. 设置偏移参数：
   - **位置偏移**：在原位置基础上增加的偏移量
   - **旋转偏移(度)**：在原旋转基础上增加的角度
   - **缩放倍数**：原缩放值乘以此倍数

#### 绝对值模式
1. 勾选"使用绝对值（覆盖原值）"
2. 设置绝对值：
   - **绝对位置**：直接设置为此位置值
   - **绝对旋转(度)**：直接设置为此旋转角度
   - **绝对缩放**：直接设置为此缩放值

### 4. 快速设置按钮
- **重置为零/一**：快速重置所有参数
- **上移0.5单位**：快速设置Y轴偏移+0.5
- **缩放0.8倍**：快速设置缩放为0.8倍

### 5. 应用修改
1. 确认所有选择和参数设置无误
2. 点击绿色"应用统一修改到所有选中预制体"按钮
3. 在确认对话框中检查修改详情，点击"确定"执行

## 实际应用示例

### 示例1：快速调整所有角色模型位置
```
目标：将所有JueSe_开头的预制体向上移动0.5单位

操作步骤：
1. 点击"选择所有JueSe_*"按钮
2. 确认为偏移模式
3. 设置位置偏移为 (0, 0.5, 0)
4. 点击"应用统一修改到所有选中预制体"
5. 确认对话框中点击"确定"

结果：所有JueSe_预制体内的Spine_XiaoGua_xxx节点都向上移动0.5单位
```

### 示例2：批量缩放特定英雄模型
```
目标：将包含"001"的预制体缩小到0.8倍

操作步骤：
1. 在搜索过滤框输入"001"
2. 系统自动选中所有包含"001"的预制体
3. 设置缩放倍数为 (0.8, 0.8, 0.8)
4. 点击"应用统一修改到所有选中预制体"

结果：所有包含"001"的预制体内Spine节点都缩小到0.8倍
```

### 示例3：统一设置绝对位置
```
目标：将选中的所有预制体Spine节点位置统一设为(0, -1, 0)

操作步骤：
1. 选择要修改的预制体（任意方式）
2. 勾选"使用绝对值（覆盖原值）"
3. 设置绝对位置为 (0, -1, 0)
4. 点击"应用统一修改到所有选中预制体"

结果：所有选中预制体内的Spine节点位置都变为(0, -1, 0)
```

## 界面元素说明

### 快速选择工具区域
- **刷新列表**：重新扫描预制体文件夹
- **全选/全不选/反选**：快速选择操作
- **搜索过滤**：实时搜索并自动选择匹配项
- **模式选择按钮**：预设的常用选择模式
- **自定义模式**：用户自定义前缀匹配

### 统一Transform修改设置区域
- **使用绝对值开关**：切换偏移模式和绝对值模式
- **参数设置字段**：根据模式显示不同的参数输入框
- **快速设置按钮**：常用参数的快速设置

### 预制体列表区域
- **分组显示**：按spine和uiSpine文件夹分组
- **实时统计**：显示选中数量和Spine节点总数
- **高亮显示**：选中的预制体会黄色高亮
- **节点信息**：显示每个预制体包含的Spine节点名称

## 技术优势

### 🚀 性能优化
- **智能批量处理**：一次操作处理所有选中预制体
- **内存友好**：不保存详细节点信息，减少内存占用
- **快速扫描**：优化的文件扫描算法

### 🎯 用户体验
- **操作简化**：3步完成批量修改（选择→设置→应用）
- **智能提示**：实时显示选择状态和统计信息
- **安全确认**：修改前详细确认对话框

### 🛡️ 数据安全
- **非破坏性预览**：设置阶段不修改实际文件
- **确认机制**：必须确认才能执行修改
- **详细日志**：完整的操作记录便于追踪

## 注意事项

### 重要提醒
1. **批量操作影响范围大**：请在重要修改前备份项目
2. **确认选择范围**：注意查看统计信息确认选中的预制体数量
3. **测试小批量**：首次使用建议先选择少量预制体测试效果

### 最佳实践
1. **使用搜索过滤**：通过搜索快速定位目标预制体
2. **优先偏移模式**：除非需要统一值，否则建议使用偏移模式
3. **验证结果**：修改完成后在游戏中验证效果

## 故障排除

### 常见问题

**Q：搜索过滤没有自动选中预制体**
A：确保搜索关键词正确，搜索是实时的，输入后会立即生效

**Q：统计显示的Spine节点数量为0**
A：检查预制体内是否包含以"Spine_XiaoGua_"开头的节点

**Q：修改后效果不明显**
A：检查修改参数是否设置正确，偏移量可能过小

**Q：想要撤销已应用的修改**
A：使用版本控制系统回滚，或手动设置反向参数重新修改

### 调试信息
工具在Unity控制台输出详细日志：
- 扫描结果统计
- 模式选择操作结果
- 修改执行详情
- 错误和警告信息

---

**工具版本**：V2.0  
**重大改进**：快速选择、统一修改、一键批量操作  
**兼容版本**：Unity 2019.4及以上 