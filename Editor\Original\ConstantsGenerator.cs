using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;

public class ConstantsGenerator : Editor
{
    private string fileName = "Constant_Audio";

    private string directory = default;

    private StreamWriter streamWriter = default;

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        directory = Application.dataPath + "/Scripts/Commons";

        if (GUILayout.Button("Generate Constants"))
        {
            Debug.Log("<color=green> Path: </color>" + Application.dataPath + "/Scripts/Commons");
            string path = GetExistingFile();

            if(path == null)
            {
                path = Path.Combine(directory, fileName + ".cs");
            }


            streamWriter = new StreamWriter(path, false);

            streamWriter.WriteLine("using System.Collections;");
            streamWriter.WriteLine("using System.Collections.Generic;");
            streamWriter.WriteLine("using UnityEngine;");
            streamWriter.WriteLine();
            streamWriter.WriteLine("public partial class Constants");
            streamWriter.WriteLine("{");
            streamWriter.WriteLine("    public static class Audio");
            streamWriter.WriteLine("    {");
            streamWriter.WriteLine("    }");
            streamWriter.WriteLine("}");
            streamWriter.WriteLine();
            streamWriter.Close();
            AssetDatabase.Refresh();
        }
        
    }


    public string GetExistingFile()
    {
        Debug.Log("<color=cyan> Path </color>" + Directory.GetFiles(directory));

        foreach(var file in Directory.GetFiles(directory,"*.cs",SearchOption.AllDirectories))
        {
            if(Path.GetFileNameWithoutExtension(file) == fileName)
            {
                return file;
            }
        }

        return null;
    }



}
