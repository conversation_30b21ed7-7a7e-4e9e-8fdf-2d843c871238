using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using System.Linq;
using System.Text.RegularExpressions;

public class SpinePrefabTransformBatchTool : EditorWindow
{
    // 工具版本信息
    private const string TOOL_VERSION = "V2.0";
    private const string TOOL_TITLE = "Spine预制体批量Transform修改工具";
    
    // 文件夹路径
    private const string SPINE_FOLDER_PATH = "Assets/Temp/model/spine";
    private const string UI_SPINE_FOLDER_PATH = "Assets/Temp/model/uiSpine";
    
    // GUI样式
    private Vector2 scrollPosition;
    private bool showSpineFolder = true;
    private bool showUISpineFolder = true;
    
    // 快速选择相关
    private string searchFilter = "";
    private string namePattern = "";
    
    // 预制体信息类
    [System.Serializable]
    public class PrefabInfo
    {
        public bool isSelected = false;
        public string prefabPath;
        public string prefabName;
        public int spineNodeCount = 0;
        public List<string> spineNodeNames = new List<string>();
        
        public PrefabInfo(string path, string name)
        {
            prefabPath = path;
            prefabName = name;
        }
    }
    
    // 数据存储
    private List<PrefabInfo> spinePrefabs = new List<PrefabInfo>();
    private List<PrefabInfo> uiSpinePrefabs = new List<PrefabInfo>();
    
    // 统一Transform修改参数
    private Vector3 unifiedPositionOffset = Vector3.zero;
    private Vector3 unifiedRotationOffset = Vector3.zero;
    private Vector3 unifiedScaleMultiplier = Vector3.one;
    private bool useAbsoluteValues = false;
    private Vector3 absolutePosition = Vector3.zero;
    private Vector3 absoluteRotation = Vector3.zero;
    private Vector3 absoluteScale = Vector3.one;
    
    [MenuItem("Tools/Spine预制体批量Transform修改工具")]
    public static void ShowWindow()
    {
        SpinePrefabTransformBatchTool window = GetWindow<SpinePrefabTransformBatchTool>();
        window.titleContent = new GUIContent(TOOL_TITLE);
        window.minSize = new Vector2(800, 600);
        window.Show();
    }
    
    private void OnEnable()
    {
        RefreshPrefabList();
    }
    
    private void OnGUI()
    {
        EditorGUILayout.BeginVertical();
        
        // 标题和版本信息
        EditorGUILayout.LabelField(TOOL_TITLE + " " + TOOL_VERSION, EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // 快速选择区域
        DrawQuickSelectionArea();
        
        EditorGUILayout.Space();
        
        // 统一Transform修改设置
        DrawUnifiedTransformSettings();
        
        EditorGUILayout.Space();
        
        // 滚动区域开始
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        // 显示spine文件夹预制体
        showSpineFolder = EditorGUILayout.Foldout(showSpineFolder, GetFolderDisplayText("Spine文件夹预制体", spinePrefabs));
        if (showSpineFolder)
        {
            EditorGUI.indentLevel++;
            DrawSimplifiedPrefabList(spinePrefabs, "spine");
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // 显示uiSpine文件夹预制体
        showUISpineFolder = EditorGUILayout.Foldout(showUISpineFolder, GetFolderDisplayText("UISpine文件夹预制体", uiSpinePrefabs));
        if (showUISpineFolder)
        {
            EditorGUI.indentLevel++;
            DrawSimplifiedPrefabList(uiSpinePrefabs, "uiSpine");
            EditorGUI.indentLevel--;
        }
        
        // 滚动区域结束
        EditorGUILayout.EndScrollView();
        
        EditorGUILayout.Space();
        
        // 底部操作按钮
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("应用统一修改到所有选中预制体", GUILayout.Height(40)))
        {
            ApplyUnifiedChanges();
        }
        GUI.backgroundColor = Color.yellow;
        if (GUILayout.Button("重置所有设置", GUILayout.Height(40)))
        {
            ResetAllSettings();
        }
        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }
    
    private void DrawQuickSelectionArea()
    {
        EditorGUILayout.LabelField("快速选择工具", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginVertical("box");
        
        // 第一行：刷新和基础选择
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("刷新列表", GUILayout.Height(25)))
        {
            RefreshPrefabList();
        }
        if (GUILayout.Button("全选", GUILayout.Height(25)))
        {
            SelectAllPrefabs(true);
        }
        if (GUILayout.Button("全不选", GUILayout.Height(25)))
        {
            SelectAllPrefabs(false);
        }
        if (GUILayout.Button("反选", GUILayout.Height(25)))
        {
            InvertSelection();
        }
        EditorGUILayout.EndHorizontal();
        
        // 第二行：搜索过滤
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("搜索过滤:", GUILayout.Width(60));
        string newSearchFilter = EditorGUILayout.TextField(searchFilter);
        if (newSearchFilter != searchFilter)
        {
            searchFilter = newSearchFilter;
            ApplySearchFilter();
        }
        if (GUILayout.Button("清除", GUILayout.Width(50)))
        {
            searchFilter = "";
            SelectAllPrefabs(false);
        }
        EditorGUILayout.EndHorizontal();
        
        // 第三行：模式选择
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("选择所有JueSe_*", GUILayout.Height(25)))
        {
            SelectByPattern("JueSe_");
        }
        if (GUILayout.Button("选择所有Spine_XiaoGua_*", GUILayout.Height(25)))
        {
            SelectByPattern("Spine_XiaoGua_");
        }
        if (GUILayout.Button("选择所有UIJueSe_*", GUILayout.Height(25)))
        {
            SelectByPattern("UIJueSe_");
        }
        EditorGUILayout.EndHorizontal();
        
        // 第四行：自定义模式
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("自定义模式:", GUILayout.Width(70));
        namePattern = EditorGUILayout.TextField(namePattern);
        if (GUILayout.Button("选择匹配项", GUILayout.Width(80)))
        {
            SelectByPattern(namePattern);
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }
    
    private void DrawUnifiedTransformSettings()
    {
        EditorGUILayout.LabelField("统一Transform修改设置", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginVertical("box");
        
        // 修改模式选择
        useAbsoluteValues = EditorGUILayout.Toggle("使用绝对值（覆盖原值）", useAbsoluteValues);
        
        EditorGUILayout.Space();
        
        if (useAbsoluteValues)
        {
            EditorGUILayout.LabelField("绝对值设置（将直接替换所有节点的Transform）", EditorStyles.boldLabel);
            absolutePosition = EditorGUILayout.Vector3Field("绝对位置", absolutePosition);
            absoluteRotation = EditorGUILayout.Vector3Field("绝对旋转(度)", absoluteRotation);
            absoluteScale = EditorGUILayout.Vector3Field("绝对缩放", absoluteScale);
        }
        else
        {
            EditorGUILayout.LabelField("偏移值设置（在原值基础上进行修改）", EditorStyles.boldLabel);
            unifiedPositionOffset = EditorGUILayout.Vector3Field("位置偏移", unifiedPositionOffset);
            unifiedRotationOffset = EditorGUILayout.Vector3Field("旋转偏移(度)", unifiedRotationOffset);
            unifiedScaleMultiplier = EditorGUILayout.Vector3Field("缩放倍数", unifiedScaleMultiplier);
        }
        
        EditorGUILayout.Space();
        
        // 快速设置按钮
        EditorGUILayout.LabelField("快速设置:", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("重置为零/一"))
        {
            if (useAbsoluteValues)
            {
                absolutePosition = Vector3.zero;
                absoluteRotation = Vector3.zero;
                absoluteScale = Vector3.one;
            }
            else
            {
                unifiedPositionOffset = Vector3.zero;
                unifiedRotationOffset = Vector3.zero;
                unifiedScaleMultiplier = Vector3.one;
            }
        }
        if (GUILayout.Button("上移0.5单位"))
        {
            if (useAbsoluteValues)
                absolutePosition.y += 0.5f;
            else
                unifiedPositionOffset.y = 0.5f;
        }
        if (GUILayout.Button("缩放0.8倍"))
        {
            if (useAbsoluteValues)
                absoluteScale = Vector3.one * 0.8f;
            else
                unifiedScaleMultiplier = Vector3.one * 0.8f;
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }
    
    private void DrawSimplifiedPrefabList(List<PrefabInfo> prefabList, string folderType)
    {
        var filteredList = GetFilteredPrefabList(prefabList);
        
        foreach (PrefabInfo prefab in filteredList)
        {
            EditorGUILayout.BeginHorizontal("box");
            
            // 预制体选择框和名称
            bool oldSelected = prefab.isSelected;
            prefab.isSelected = EditorGUILayout.Toggle(prefab.isSelected, GUILayout.Width(20));
            
            // 如果选择状态改变，更新显示
            if (oldSelected != prefab.isSelected)
            {
                Repaint();
            }
            
            // 预制体名称（如果选中则高亮显示）
            GUIStyle nameStyle = prefab.isSelected ? EditorStyles.boldLabel : EditorStyles.label;
            if (prefab.isSelected)
            {
                GUI.backgroundColor = Color.yellow;
            }
            EditorGUILayout.LabelField(prefab.prefabName, nameStyle);
            GUI.backgroundColor = Color.white;
            
            // 显示Spine节点数量
            EditorGUILayout.LabelField($"({prefab.spineNodeCount}个Spine节点)", GUILayout.Width(120));
            
            // 显示节点名称列表
            if (prefab.spineNodeNames.Count > 0)
            {
                string nodeNames = string.Join(", ", prefab.spineNodeNames.Take(3));
                if (prefab.spineNodeNames.Count > 3)
                    nodeNames += "...";
                EditorGUILayout.LabelField($"[{nodeNames}]", GUILayout.Width(200));
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        // 显示过滤信息
        if (filteredList.Count != prefabList.Count)
        {
            EditorGUILayout.LabelField($"显示 {filteredList.Count}/{prefabList.Count} 个预制体", EditorStyles.miniLabel);
        }
    }
    
    private List<PrefabInfo> GetFilteredPrefabList(List<PrefabInfo> originalList)
    {
        if (string.IsNullOrEmpty(searchFilter))
            return originalList;
            
        return originalList.Where(p => p.prefabName.ToLower().Contains(searchFilter.ToLower())).ToList();
    }
    
    private string GetFolderDisplayText(string folderName, List<PrefabInfo> prefabList)
    {
        int selectedCount = prefabList.Count(p => p.isSelected);
        int totalSpineNodes = prefabList.Where(p => p.isSelected).Sum(p => p.spineNodeCount);
        
        return $"{folderName} ({selectedCount}/{prefabList.Count}选中, 共{totalSpineNodes}个Spine节点)";
    }
    
    private void RefreshPrefabList()
    {
        Debug.Log($"{TOOL_VERSION} 开始刷新预制体列表...");
        
        spinePrefabs.Clear();
        uiSpinePrefabs.Clear();
        
        // 扫描spine文件夹
        if (Directory.Exists(SPINE_FOLDER_PATH))
        {
            string[] spineFiles = Directory.GetFiles(SPINE_FOLDER_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            foreach (string file in spineFiles)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                PrefabInfo prefabInfo = new PrefabInfo(file, fileName);
                AnalyzePrefabSpineNodes(prefabInfo);
                spinePrefabs.Add(prefabInfo);
            }
        }
        
        // 扫描uiSpine文件夹
        if (Directory.Exists(UI_SPINE_FOLDER_PATH))
        {
            string[] uiSpineFiles = Directory.GetFiles(UI_SPINE_FOLDER_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            foreach (string file in uiSpineFiles)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                PrefabInfo prefabInfo = new PrefabInfo(file, fileName);
                AnalyzePrefabSpineNodes(prefabInfo);
                uiSpinePrefabs.Add(prefabInfo);
            }
        }
        
        Debug.Log($"{TOOL_VERSION} 扫描完成: spine文件夹{spinePrefabs.Count}个预制体, uiSpine文件夹{uiSpinePrefabs.Count}个预制体");
    }
    
    private void AnalyzePrefabSpineNodes(PrefabInfo prefabInfo)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabInfo.prefabPath);
        if (prefab == null) return;
        
        // 递归搜索所有Spine_XiaoGua_xxx节点
        Transform[] allTransforms = prefab.GetComponentsInChildren<Transform>(true);
        foreach (Transform t in allTransforms)
        {
            if (t.name.StartsWith("Spine_XiaoGua_"))
            {
                prefabInfo.spineNodeNames.Add(t.name);
                prefabInfo.spineNodeCount++;
            }
        }
    }
    
    private void SelectAllPrefabs(bool selected)
    {
        foreach (PrefabInfo prefab in spinePrefabs)
        {
            prefab.isSelected = selected;
        }
        foreach (PrefabInfo prefab in uiSpinePrefabs)
        {
            prefab.isSelected = selected;
        }
    }
    
    private void InvertSelection()
    {
        foreach (PrefabInfo prefab in spinePrefabs)
        {
            prefab.isSelected = !prefab.isSelected;
        }
        foreach (PrefabInfo prefab in uiSpinePrefabs)
        {
            prefab.isSelected = !prefab.isSelected;
        }
    }
    
    private void ApplySearchFilter()
    {
        if (string.IsNullOrEmpty(searchFilter))
            return;
            
        // 自动选中匹配搜索条件的预制体
        foreach (PrefabInfo prefab in spinePrefabs)
        {
            prefab.isSelected = prefab.prefabName.ToLower().Contains(searchFilter.ToLower());
        }
        foreach (PrefabInfo prefab in uiSpinePrefabs)
        {
            prefab.isSelected = prefab.prefabName.ToLower().Contains(searchFilter.ToLower());
        }
    }
    
    private void SelectByPattern(string pattern)
    {
        if (string.IsNullOrEmpty(pattern))
            return;
            
        int selectedCount = 0;
        
        foreach (PrefabInfo prefab in spinePrefabs)
        {
            if (prefab.prefabName.StartsWith(pattern))
            {
                prefab.isSelected = true;
                selectedCount++;
            }
        }
        foreach (PrefabInfo prefab in uiSpinePrefabs)
        {
            if (prefab.prefabName.StartsWith(pattern))
            {
                prefab.isSelected = true;
                selectedCount++;
            }
        }
        
        Debug.Log($"{TOOL_VERSION} 按模式 '{pattern}' 选中了 {selectedCount} 个预制体");
    }
    
    private void ResetAllSettings()
    {
        unifiedPositionOffset = Vector3.zero;
        unifiedRotationOffset = Vector3.zero;
        unifiedScaleMultiplier = Vector3.one;
        absolutePosition = Vector3.zero;
        absoluteRotation = Vector3.zero;
        absoluteScale = Vector3.one;
        useAbsoluteValues = false;
        searchFilter = "";
        namePattern = "";
        
        Debug.Log($"{TOOL_VERSION} 所有设置已重置");
    }
    
    private void ApplyUnifiedChanges()
    {
        int processedPrefabs = 0;
        int processedNodes = 0;
        
        List<PrefabInfo> allPrefabs = new List<PrefabInfo>();
        allPrefabs.AddRange(spinePrefabs);
        allPrefabs.AddRange(uiSpinePrefabs);
        
        var selectedPrefabs = allPrefabs.Where(p => p.isSelected).ToList();
        
        if (selectedPrefabs.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", "请先选择要修改的预制体！", "确定");
            return;
        }
        
        bool confirm = EditorUtility.DisplayDialog(
            "确认修改", 
            $"将对 {selectedPrefabs.Count} 个预制体中的 {selectedPrefabs.Sum(p => p.spineNodeCount)} 个Spine节点应用统一修改。\n\n" +
            (useAbsoluteValues ? 
                $"绝对值模式:\n位置: {absolutePosition}\n旋转: {absoluteRotation}\n缩放: {absoluteScale}" :
                $"偏移模式:\n位置偏移: {unifiedPositionOffset}\n旋转偏移: {unifiedRotationOffset}\n缩放倍数: {unifiedScaleMultiplier}") +
            "\n\n确定要继续吗？", 
            "确定", "取消");
            
        if (!confirm) return;
        
        foreach (PrefabInfo prefab in selectedPrefabs)
        {
            // 加载预制体
            GameObject prefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(prefab.prefabPath);
            if (prefabAsset == null)
            {
                Debug.LogError($"{TOOL_VERSION} 无法加载预制体: {prefab.prefabPath}");
                continue;
            }
            
            // 应用修改到所有Spine_XiaoGua_xxx节点
            Transform[] allTransforms = prefabAsset.GetComponentsInChildren<Transform>(true);
            int nodeModifiedCount = 0;
            
            foreach (Transform t in allTransforms)
            {
                if (t.name.StartsWith("Spine_XiaoGua_"))
                {
                    if (useAbsoluteValues)
                    {
                        // 绝对值模式
                        t.localPosition = absolutePosition;
                        t.localEulerAngles = absoluteRotation;
                        t.localScale = absoluteScale;
                    }
                    else
                    {
                        // 偏移模式
                        t.localPosition += unifiedPositionOffset;
                        t.localEulerAngles += unifiedRotationOffset;
                        t.localScale = Vector3.Scale(t.localScale, unifiedScaleMultiplier);
                    }
                    
                    nodeModifiedCount++;
                    processedNodes++;
                }
            }
            
            if (nodeModifiedCount > 0)
            {
                // 标记为已修改
                EditorUtility.SetDirty(prefabAsset);
                processedPrefabs++;
                
                Debug.Log($"{TOOL_VERSION} 修改预制体 {prefab.prefabName} - {nodeModifiedCount}个节点");
            }
        }
        
        // 保存资源
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        string resultMessage = $"批量修改完成!\n\n处理了 {processedPrefabs} 个预制体\n修改了 {processedNodes} 个Spine节点";
        EditorUtility.DisplayDialog("修改完成", resultMessage, "确定");
        
        Debug.Log($"{TOOL_VERSION} " + resultMessage.Replace("\n", " "));
    }
}